import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM } from '../utils/callLLM.js';
import { ServerBlockNoteEditor } from '@blocknote/server-util';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

export interface StudioContentParams {
  taskDescription: string;
  selectedCompanyContent: any;
  selectedPersonas: string[];
  personas: any[];
  selectedIcps: string[];
  icps: any[];
  selectedResearch: string[];
  researchItems: any[];
  selectedDocuments: any[];
  seoKeywords: string[];
  trendKeywords: string[];
  companyBrand: any;
}

export interface StudioContentResult {
  content: string;
  content_editor_template: any[];
}

/**
 * Converts text content to BlockNote blocks format using official BlockNote server utilities
 */
async function convertTextToBlocks(text: string): Promise<any[]> {
  if (!text || text.trim() === '') {
    return [];
  }

  try {
    // Create a server-side BlockNote editor instance
    const editor = ServerBlockNoteEditor.create();

    // Use BlockNote's official markdown parser to convert text to blocks
    const blocks = await editor.tryParseMarkdownToBlocks(text);

    return blocks;
  } catch (error) {
    console.error('Error converting text to blocks with BlockNote:', error);

    // Fallback to simple paragraph blocks if BlockNote parsing fails
    const paragraphs = text.split('\n\n').filter(p => p.trim() !== '');

    return paragraphs.map(paragraph => ({
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: paragraph.trim(),
          styles: {},
        },
      ],
    }));
  }
}

/**
 * Generate context blocks for audience information
 */
function generateAudienceContext(
  selectedPersonas: string[],
  personas: any[],
  selectedIcps: string[],
  icps: any[]
): string {
  const personasSection =
    selectedPersonas.length > 0
      ? `
    <Personas>
        ${personas
          .filter((p) => selectedPersonas.includes(p.id))
          .map((p) => {
            const personaData = p.data && typeof p.data === 'object' ? p.data : {};
            return `<Persona name="${p.name}">\n<Description>${JSON.stringify((personaData as any).data) || 'Target persona'}</Description>\n</Persona>`;
          })
          .join('\n')}
    </Personas>`
      : '';

  const icpsSection =
    selectedIcps.length > 0
      ? `
    <IdealCustomerProfiles>
        ${icps
          .filter((i) => selectedIcps.includes(i.id))
          .map((i) => {
            const icpData = i.data && typeof i.data === 'object' ? i.data : {};
            return `<ICP name="${i.name}">\n<Description>${JSON.stringify((icpData as any).data) || 'Ideal customer profile'}</Description>\n</ICP>`;
          })
          .join('\n')}
    </IdealCustomerProfiles>`
      : '';

  return `
    <AUDIENCE_CONTEXT>
        ${personasSection}
        ${icpsSection}
    </AUDIENCE_CONTEXT>
  `;
}

/**
 * Generate research materials context
 */
function generateResearchMaterials(selectedResearch: string[], researchItems: any[]): string {
  if (selectedResearch.length === 0) return '';

  const researchSection = researchItems
    .filter((r) => selectedResearch.includes(r.id))
    .map((r) => {
      const researchData = r.data && typeof r.data === 'object' ? r.data : {};
      return `<ResearchItem title="${r.name}">\n<Content>${JSON.stringify((researchData as any).data) || 'Research content'}</Content>\n</ResearchItem>`;
    })
    .join('\n');

  return `
    <RESEARCH_MATERIALS>
        ${researchSection}
    </RESEARCH_MATERIALS>
  `;
}

/**
 * Generate product knowledge base context
 */
function generateProductKnowledgeBase(selectedDocuments: any[]): string {
  if (selectedDocuments.length === 0) return '';

  const documentsSection = selectedDocuments
    .map((doc) => {
      return `<Document title="${doc.name}">\n<Content>${doc.content || 'Document content'}</Content>\n</Document>`;
    })
    .join('\n');

  return `
    <PRODUCT_KNOWLEDGE_BASE>
        ${documentsSection}
    </PRODUCT_KNOWLEDGE_BASE>
  `;
}

/**
 * Compiles a Langfuse prompt with the given variables
 */
async function compilePrompt(params: StudioContentParams): Promise<string> {
  const prompt = await langfuse.getPrompt("generate_content_body_v2", undefined, { label: "production" });
  
  // Generate the context blocks for compilation
  const personasBlock = generateAudienceContext(params.selectedPersonas, params.personas, [], []);
  const icpsBlock = generateAudienceContext([], [], params.selectedIcps, params.icps);
  const researchBlock = generateResearchMaterials(params.selectedResearch, params.researchItems);
  const documentsBlock = generateProductKnowledgeBase(params.selectedDocuments);

  // Compile the prompt with the variables
  const compiled = prompt.compile({
    channel: params.selectedCompanyContent?.channel || 'Not specified',
    content_type: params.selectedCompanyContent?.content_type || 'Not specified',
    task_description: params.taskDescription,
    personas_block: personasBlock,
    icps_block: icpsBlock,
    research_block: researchBlock,
    documents_block: documentsBlock,
    seo_keywords: params.seoKeywords.join(', '),
    trend_keywords: params.trendKeywords.join(', '),
    brand_guidelines: JSON.stringify(params.companyBrand)
  });

  return compiled;
}

/**
 * Generates studio content using Langfuse prompt and LLM
 */
export async function generateStudioContent(params: StudioContentParams): Promise<StudioContentResult> {
  try {
    console.log('Generating studio content with params:', params);

    // Compile the prompt with parameters
    const promptText = await compilePrompt(params);
    console.log('Compiled prompt:', promptText);

    // Call LLM with compiled prompt
    const response = await callLLM(
      promptText,
      {
        temperature: 0.7,
      },
      "google/gemini-2.5-pro-preview",
      { parse: false }
    );

    console.log('LLM response:', response);

    // Convert text content to BlockNote blocks using official server utilities
    const contentBlocks = await convertTextToBlocks(response);

    // Return standardized response format
    return {
      content: response,
      content_editor_template: contentBlocks,
    };

  } catch (error) {
    console.error('Error generating studio content:', error);
    throw new Error(
      `Failed to generate studio content: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
