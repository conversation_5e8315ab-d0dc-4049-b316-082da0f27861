import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const supabase = getSupabaseServerClient();
    const { data, error } = await supabase.auth.getUser();
    
    if (!data.user || error) {
      return NextResponse.json({ error: 'User not found' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    
    // Validate required fields
    if (!body.taskDescription || !body.selectedCompanyContent) {
      return NextResponse.json(
        { error: 'Missing required fields: taskDescription and selectedCompanyContent' },
        { status: 400 }
      );
    }

    // Forward request to sb-server
    const pushServerUrl = process.env.NEXT_PUBLIC_PUSH_SERVER || 'http://localhost:8080';
    const response = await fetch(`${pushServerUrl}/generate-studio-content`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error from sb-server:', errorText);
      return NextResponse.json(
        { error: 'Failed to generate content from server' },
        { status: response.status }
      );
    }

    const result = await response.json();
    
    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error('Error in generate-studio-content API route:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
